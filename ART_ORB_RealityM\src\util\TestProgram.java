package util;

public class TestProgram {
	static {
		try {
			// Try loading with full path first
			System.load("C:\\Users\\<USER>\\Desktop\\java workspace\\ART_ORB_RealityM\\programlib.dll");
		} catch (UnsatisfiedLinkError e1) {
			try {
				// Fallback to library name
				System.loadLibrary("programlib");
			} catch (UnsatisfiedLinkError e2) {
				System.err.println("Failed to load programlib.dll:");
				System.err.println("Full path attempt: " + e1.getMessage());
				System.err.println("Library name attempt: " + e2.getMessage());
				throw e2;
			}
		}
	}

	public static void main(String[] args) {
		// System.out.println(new bessj0().isCorrect(-37.62759377580369));
		System.out.println(TestProgram.test_airy(-37.62759377580369));
		System.out.println(TestProgram.test_bessj(219.85509600636087, 135.87255603090216));
	}

	public static native boolean test_airy(double a);

	public static native boolean test_bessj(double a, double b);

	public static native boolean test_bessj0(double a);

	public static native boolean test_cel(double a, double b, double c, double d);

	public static native boolean test_el2(double a, double b, double c, double d);

	public static native boolean test_erfcc(double a);

	public static native boolean test_gammq(double a, double b);

	public static native boolean test_golden(double a, double b, double c);

	public static native boolean test_plgndr(double a, double b, double c);

	public static native boolean test_probks(double a);

	public static native boolean test_sncndn(double a, double b);

	public static native boolean test_tanh(double a);
}
